{"name": "project-2025-nhb-gamification-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@govtechsg/sgds-web-component": "^3.2.0", "@tanstack/react-query": "^5.85.5", "lucide-react": "^0.540.0", "next": "15.5.0", "react": "19.1.1", "react-dom": "19.1.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^24", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.0", "tailwindcss": "^4", "typescript": "^5"}}