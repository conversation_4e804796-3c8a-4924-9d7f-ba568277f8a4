import { useQuery } from "@tanstack/react-query";
import { ReactNode } from "react";

export async function fetchGame(url: string) {
  const res = await fetch("http://localhost:3000?url=" + url);
  if (!res.ok) {
    throw new Error("Failed to fetch data");
  }
  return res.json();
}

export const gameQueryKey = ["game"];

export const useGame = () => {
  return useQuery({
    queryKey: gameQuery<PERSON>ey,
    queryFn: () => fetchGame("explore-hcmc-23"),
    staleTime: 1000 * 60 * 1, // cache 1 minutes
  });
};

export function GameDataLoader({ children }: { children: ReactNode }) {
  const { isLoading, error } = useGame();

  if (isLoading) return <p>Loading game data...</p>;
  if (error) return <p>Failed to load game data</p>;

  return children;
}
