"use client";

import { useState } from "react";

import { Header } from "@/themes/nightwalk/components/Header";
import "./globals.css";

import { Footer } from "@/themes/nightwalk/components/Footer";
import SgdsMasthead from "@govtechsg/sgds-web-component/react/masthead/index.js";
import { Bebas_Neue, Open_Sans } from "next/font/google";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { GameDataLoader } from "@/services/GameDataLoader";

const bebasSans = Bebas_Neue({
  variable: "--font-bebas-sans",
  weight: "400",
  subsets: ["latin"],
});

const openSans = Open_Sans({
  variable: "--font-open-sans",
  weight: ["400"],
  subsets: ["latin"],
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const [queryClient] = useState(() => new QueryClient());

  return (
    <html lang="en">
      <body className={`${bebasSans.variable} ${openSans.variable} antialiased font-open-sans`}>
        <QueryClientProvider client={queryClient}>
          <GameDataLoader>
            <SgdsMasthead />
            <div className="container flex flex-col ">
              <Header />
              {children}
              <Footer />
            </div>
          </GameDataLoader>
        </QueryClientProvider>
      </body>
    </html>
  );
}
